# Portal.Gateway Entegrasyon Testleri

## Genel Bakış

<PERSON> belge, Portal.Gateway projesi için kapsamlı bir entegrasyon test çerçevesi kılavuzu sunmaktadır. Çerçeve, Gateway API uç noktalarını ve bunların veritabanı ve mesaj veri yolu ile etkileşimlerini Docker konteynerleri kullanarak izole bir ortamda test etmek için tasarlanmıştır.

## Mimari

Entegrasyon test projesi aşağıdaki mimariyi kullanır:

- **Testcontainers**: Test için izole PostgreSQL ve RabbitMQ konteynerlerini başlatır
- **WebApplicationFactory**: Test sırasında API'yi barındırmak için özel uygulama
- **xUnit**: Paylaşılan test fikstürleri ile test çerçevesi
- **Bogus**: Test verisi oluşturmak için kütüphane

## Temel Bileşenler

### SharedTestFixture

Bir koleksiyondaki tüm testler için test ortamını bir kez başlatan paylaşılan bir fikstür. `PortalWebApplicationFactory`'nin yaşam döngüsünü yönetir.

```csharp
public class SharedTestFixture : IAsyncLifetime
{
    public PortalWebApplicationFactory Factory { get; } = new();

    public async Task InitializeAsync()
    {
        await Factory.InitializeAsync();
    }

    public async Task DisposeAsync()
    {
        await Factory.DisposeAsync();
    }
}
```

### PortalWebApplicationFactory

Test ortamını kuran `WebApplicationFactory<T>` özel uygulaması:

- PostgreSQL konteynerini oluşturur ve yapılandırır
- Test için servisleri yapılandırır
- Test verilerini ekler
- Harici bağımlılıkları taklit eder

### DatabaseContainer

Test için PostgreSQL Docker konteynerini yönetir:

- İzole bir veritabanı örneği oluşturur
- Test uygulamasına bağlantı dizelerini sağlar
- Konteyner yaşam döngüsünü yönetir

### EventBusContainer

Mesaj veri yolu etkileşimlerini test etmek için RabbitMQ Docker konteynerini yönetir:

- İzole bir mesaj aracısı örneği oluşturur
- Test uygulamasına bağlantı dizelerini sağlar
- Konteyner yaşam döngüsünü yönetir

### BaseIntegrationTest

Tüm entegrasyon testleri için temel sınıf şunları sağlar:

- Veritabanı bağlamına erişim
- Yapılandırılmış HTTP istemcisi
- Bogus veri üreteci
- Koleksiyon fikstür kurulumu

### TestDataSeeder

Test için gerekli verilerle test veritabanını dolduran yardımcı sınıf:

- Test verileri oluşturur (örneğin: ülkeler, kategoriler, kullanıcılar)
- Eklenmiş varlıklara erişim için yardımcı metodlar sağlar

## Entegrasyon Testleri Nasıl Yazılır

### Yeni Bir Test Sınıfı Oluşturma

1. Uygun modül klasöründe yeni bir test sınıfı oluşturun
2. `BaseIntegrationTest`'ten miras alın

Örnek:

```csharp
public sealed class ExampleServiceTest : BaseIntegrationTest
{
    public ExampleServiceTest(SharedTestFixture fixture) : base(fixture)
    {
    }

    // Test metodları buraya gelir
}
```

### Test Metodları Yazma

Testler Düzenleme-Eylem-Doğrulama (Arrange-Act-Assert) desenini takip etmelidir:

```csharp
[Fact]
internal async Task ProcessRequest_WhenValidRequest_ShouldCreateRecord()
{
    // Arrange - Düzenleme
    var entity = await CreateTestEntity();
    var request = new ExampleApiRequest
    {
        EntityId = entity.Id,
        Value = 100.00m,
        Type = "TipA",
        Details = new DetailApiRequest
        {
            PropertyA = "Değer1",
            PropertyB = "Değer2",
            PropertyC = "Değer3"
        }
    };

    // Act - Eylem
    var response = await HttpClient.PostAsJsonAsync(
        ApiMethodName.ExampleService.ProcessRequest,
        request);
    var responseBody = await response.Content.ReadAsStringAsync();
    var result = JsonConvert.DeserializeObject<ApiResponse<ExampleResponseDto>>(responseBody);

    // Assert - Doğrulama
    Assert.True(result.IsSuccess);
    Assert.NotNull(result.Data);
    Assert.NotNull(result.Data.ReferenceId);

    // Veritabanı kaydının oluşturulduğunu doğrula
    var exampleRecord = await DbContext.ExampleRecords
        .FirstOrDefaultAsync(p => p.ReferenceId == result.Data.ReferenceId);
    Assert.NotNull(exampleRecord);
    Assert.Equal(request.Value, exampleRecord.Value);
}
```

### Veritabanı İşlemlerini Test Etme

Veritabanı bağlamına doğrudan `DbContext` özelliği aracılığıyla erişin:

```csharp
[Fact]
internal async Task GetEntityData_WhenEntityExists_ShouldReturnCorrectData()
{
    // Arrange - Veritabanında test varlığı oluştur
    var entity = new ExampleEntity
    {
        Name = Faker.Random.String2(10),
        Description = Faker.Lorem.Sentence(),
        Status = "Aktif"
    };

    await DbContext.ExampleEntities.AddAsync(entity);
    await DbContext.SaveChangesAsync();

    // Act - API'yi çağır
    var response = await HttpClient.GetAsync($"api/examples/{entity.Id}");
    var result = await response.Content.ReadFromJsonAsync<ApiResponse<EntityDto>>();

    // Assert - Yanıtı doğrula
    Assert.True(result.IsSuccess);
    Assert.Equal(entity.Name, result.Data.Name);
}
```

### Test Verilerini Kullanma

Rastgele test verileri oluşturmak için Faker örneğini kullanın:

```csharp
var metadata = new MetadataApiRequest
{
    Alan1 = Faker.Random.String2(10),
    Alan2 = Faker.Random.Number(100),
    Alan3 = Faker.Random.Bool(),
    Alan4 = Faker.Date.Future()
};
```

Veya önceden eklenmiş varlıkları almak için TestDataSeeder'ı kullanın:

```csharp
var category = TestDataSeeder.GetCategory();
var status = TestDataSeeder.GetStatus();
```

## Gelişmiş Test Senaryoları

### Farklı Kullanıcı İzinleriyle Test

HTTP istemcisini farklı yetkilendirme başlıklarıyla yapılandırın:

```csharp
// Admin kullanıcısı ile test
[Fact]
internal async Task AdminEndpoint_WithAdminUser_ShouldSucceed()
{
    // Arrange - İstemciyi admin token'ı ile yapılandır
    var adminToken = GenerateAdminToken();
    HttpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {adminToken}");

    // Act
    var response = await HttpClient.GetAsync("api/yonetim/istatistikler");

    // Assert
    Assert.Equal(HttpStatusCode.OK, response.StatusCode);
}

// Normal kullanıcı ile test
[Fact]
internal async Task AdminEndpoint_WithRegularUser_ShouldFail()
{
    // Arrange - İstemciyi normal kullanıcı token'ı ile yapılandır
    var userToken = GenerateUserToken();
    HttpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {userToken}");

    // Act
    var response = await HttpClient.GetAsync("api/yonetim/istatistikler");

    // Assert
    Assert.Equal(HttpStatusCode.Forbidden, response.StatusCode);
}
```

### Event Bus Entegrasyonunu Test Etme

Mesaj yayınlama ve tüketmeyi test etmek için:

1. Gerçek bir RabbitMQ bağlantısı kullanmak için `PortalWebApplicationFactory`'yi değiştirin
2. API aracılığıyla mesajlar gönderin
3. Beklenen yan etkileri doğrulayın

```csharp
[Fact]
internal async Task EntityCreated_ShouldPublishEvent()
{
    // Arrange - Mesaj dinleyici kurulumu
    var messageReceived = new TaskCompletionSource<bool>();
    var consumer = new TestMessageConsumer("entity.created", () => messageReceived.SetResult(true));

    // Act - API aracılığıyla varlık oluştur
    var createEntityRequest = new CreateEntityApiRequest
    {
        PropertyA = "Değer1",
        PropertyB = 123
    };

    await HttpClient.PostAsJsonAsync("api/entities", createEntityRequest);

    // Assert - Mesajın yayınlandığını doğrula
    var receivedWithinTimeout = await messageReceived.Task.WaitAsync(TimeSpan.FromSeconds(5));
    Assert.True(receivedWithinTimeout, "Varlık oluşturuldu olayı yayınlanmadı");
}
```

## En İyi Uygulamalar

1. **İzolasyon**: Her test kendi kendine yeterli olmalı ve diğer testlere bağımlı olmamalıdır
2. **Temizlik**: Testler sırasında oluşturulan verileri temizlemek için veritabanı bağlamını kullanın
3. **Gerçekçi Veriler**: Gerçekçi test verileri oluşturmak için Faker kullanın
4. **Odaklanmış Testler**: Her test, işlevselliğin tek bir yönünü doğrulamalıdır
5. **Açıklayıcı İsimler**: Neyin test edildiğini açıklayan açıklayıcı test isimleri kullanın
