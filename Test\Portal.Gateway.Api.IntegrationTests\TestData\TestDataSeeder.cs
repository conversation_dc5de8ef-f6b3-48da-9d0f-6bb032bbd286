namespace Portal.Gateway.Api.IntegrationTests.TestData;

public class TestDataSeeder : ITestDataSeeder
{
    private readonly PortalDbContext _context;
    private readonly ILogger<TestDataSeeder> _logger;

    public TestDataSeeder(PortalDbContext context, ILogger<TestDataSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        _logger.LogInformation("Starting test data seeding process...");

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            _logger.LogDebug("Began database transaction for test data seeding");

            await SeedDataFromSqlFileAsync(_context, "test_data_seed_script");
            await SeedTestUsersAsync(_context);
            
            await _context.SaveChangesAsync();

            await transaction.CommitAsync();
            _logger.LogInformation("Successfully committed test data transaction");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while seeding test data. Rolling back transaction...");
            await transaction.RollbackAsync();
            _logger.LogInformation("Transaction rolled back successfully");
            throw;
        }
    }

    private async Task SeedDataFromSqlFileAsync(PortalDbContext context, string fileName)
    {
        fileName += ".sql";
        string sqlPath = Path.Combine(AppContext.BaseDirectory, "TestData", "SeedData", fileName);

        _logger.LogDebug("Attempting to seed data from SQL file at path: {SqlFilePath}", sqlPath);

        if (!File.Exists(sqlPath))
        {
            _logger.LogWarning("SQL seed file not found at path: {SqlFilePath}", sqlPath);
            return;
        }

        try
        {
            string sqlScript = await File.ReadAllTextAsync(sqlPath);

            var result = await context.Database.ExecuteSqlRawAsync(sqlScript);
            _logger.LogInformation("Executed SQL seed script. Rows affected: {RowsAffected}", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing SQL seed script from file: {SqlFilePath}", sqlPath);
            throw;
        }
    }

    private async Task SeedTestUsersAsync(PortalDbContext dbContext)
    {
        _logger.LogDebug("Starting test users seeding process...");

        if (await dbContext.User.AnyAsync())
        {
            _logger.LogInformation("Users already exist in database. Skipping test user generation.");
            return;
        }

        try
        {
            var userFaker = new Faker<User>()
                .RuleFor(u => u.Name, f => f.Name.FirstName())
                .RuleFor(u => u.Surname, f => f.Name.LastName())
                .RuleFor(u => u.Email, (f, u) => f.Internet.Email(u.Name, u.Surname))
                .RuleFor(u => u.Password, f => f.Internet.Password(12))
                .RuleFor(u => u.PhoneNumber, f => f.Phone.PhoneNumber("+### ### ### ####"));

            var users = userFaker.Generate(5);
            _logger.LogDebug("Generated {UserCount} test users using Faker", users.Count);

            await dbContext.User.AddRangeAsync(users);
            var changes = await dbContext.SaveChangesAsync();
            _logger.LogInformation("Successfully seeded {UserCount} test users to database. Changes saved: {Changes}", users.Count, changes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while seeding test users");
            throw;
        }
    }

    #region Data Access Methods

    public Provider GetRandomProvider() => GetRandom<Provider>();
    public Country GetRandomCountry() => GetRandom<Country>();
    public BranchApplicationCountry GetRandomBranchApplicationCountry() => GetRandom<BranchApplicationCountry>();
    public User GetRandomTestUser() => GetRandom<User>();

    #endregion

    #region Utility Methods

    public T GetRandom<T>() where T : class =>
        _context.Set<T>()
            .OrderBy(x => EF.Functions.Random())
            .AsNoTracking()
            .First();

    #endregion
}