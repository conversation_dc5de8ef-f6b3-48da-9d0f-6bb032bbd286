namespace Portal.Gateway.Api.IntegrationTests.TestData;

public class TestDataSeeder(PortalDbContext context, ILogger<TestDataSeeder> logger) : ITestDataSeeder
{
    private static readonly Random _random = new();

    public async Task SeedAsync()
    {
        logger.LogInformation("Starting test data seeding process...");

        await using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            logger.LogDebug("Began database transaction for test data seeding");

            await SeedDataFromSqlFileAsync(context, "test_data_seed_script");
            await SeedTestUsersAsync(context);

            // Save all changes at once for better performance
            var changes = await context.SaveChangesAsync();
            logger.LogDebug("Saved {Changes} changes to database", changes);

            await transaction.CommitAsync();
            logger.LogInformation("Successfully committed test data transaction");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while seeding test data. Rolling back transaction...");
            await transaction.RollbackAsync();
            logger.LogInformation("Transaction rolled back successfully");
            throw;
        }
    }

    private async Task SeedDataFromSqlFileAsync(PortalDbContext context, string fileName)
    {
        fileName += ".sql";
        string sqlPath = Path.Combine(AppContext.BaseDirectory, "TestData", "SeedData", fileName);

        logger.LogDebug("Attempting to seed data from SQL file at path: {SqlFilePath}", sqlPath);

        if (!File.Exists(sqlPath))
        {
            logger.LogWarning("SQL seed file not found at path: {SqlFilePath}", sqlPath);
            return;
        }

        try
        {
            string sqlScript = await File.ReadAllTextAsync(sqlPath);

            // Use ExecuteSqlRaw for better performance with pre-validated SQL scripts
            // Note: Only use this with trusted SQL files in test environment
            var result = await context.Database.ExecuteSqlRawAsync(sqlScript);
            logger.LogInformation("Executed SQL seed script. Rows affected: {RowsAffected}", result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error executing SQL seed script from file: {SqlFilePath}", sqlPath);
            throw;
        }
    }

    private async Task SeedTestUsersAsync(PortalDbContext dbContext)
    {
        logger.LogDebug("Starting test users seeding process...");

        if (await dbContext.User.AnyAsync())
        {
            logger.LogInformation("Users already exist in database. Skipping test user generation.");
            return;
        }

        try
        {
            var userFaker = new Faker<User>()
                .RuleFor(u => u.Name, f => f.Name.FirstName())
                .RuleFor(u => u.Surname, f => f.Name.LastName())
                .RuleFor(u => u.Email, (f, u) => f.Internet.Email(u.Name, u.Surname))
                .RuleFor(u => u.Password, f => f.Internet.Password(12))
                .RuleFor(u => u.PhoneNumber, f => f.Phone.PhoneNumber("+### ### ### ####"));

            var users = userFaker.Generate(5);
            logger.LogDebug("Generated {UserCount} test users using Faker", users.Count);

            await dbContext.User.AddRangeAsync(users);
            // Don't save here - let the main transaction handle all saves for better performance
            logger.LogInformation("Successfully prepared {UserCount} test users for seeding", users.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while seeding test users");
            throw;
        }
    }

    #region Data Access Methods

    public Provider GetRandomProvider() => GetRandom<Provider>();
    public Country GetRandomCountry() => GetRandom<Country>();
    public BranchApplicationCountry GetRandomBranchApplicationCountry() => GetRandom<BranchApplicationCountry>();
    public User GetRandomTestUser() => GetRandom<User>();

    #endregion

    #region Utility Methods


    public T GetRandom<T>() where T : class
    {
        var dbSet = context.Set<T>().AsNoTracking();
        var count = dbSet.Count();

        if (count == 0)
        {
            throw new InvalidOperationException($"No records found in {typeof(T).Name} table");
        }

        // Much more efficient: skip random number of records instead of ordering by random
        var skip = _random.Next(0, count);
        return dbSet.Skip(skip).FirstOrDefault()
               ?? throw new InvalidOperationException($"Failed to retrieve random {typeof(T).Name}");
    }

    #endregion
}