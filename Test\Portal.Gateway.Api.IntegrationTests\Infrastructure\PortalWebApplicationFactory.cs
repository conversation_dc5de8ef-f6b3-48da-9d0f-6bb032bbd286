using Gateway.EventBus.Connections;
using Moq;
using Portal.Gateway.ExternalServices.Contracts;

namespace Portal.Gateway.Api.IntegrationTests.Infrastructure;

/// <summary>
/// Test factory for integration tests that manages test database lifecycle
/// and provides proper service configuration for testing.
/// </summary>
public sealed class PortalWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly DatabaseContainer _databaseContainer = new();
    private readonly ILogger<PortalWebApplicationFactory> _logger;

    public PortalWebApplicationFactory()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<PortalWebApplicationFactory>();
    }

    protected override IHost CreateHost(IHostBuilder builder)
    {
        builder.ConfigureServices(ConfigureTestServices);

        var host = builder.Build();
        host.Start();

        using var scope = host.Services.CreateScope();
        InitializeDatabaseAsync(scope.ServiceProvider).GetAwaiter().GetResult();

        return host;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureTestServices(services =>
        {
            ConfigureDatabaseServices(services);
            RegisterTestServices(services);
        });
    }

    public async Task InitializeAsync() => await _databaseContainer.InitializeAsync();

    public new async Task DisposeAsync()
    {
        await _databaseContainer.DisposeAsync();
        await base.DisposeAsync();
    }

    #region Service Configuration

    /// <summary>
    /// Configures the necessary service configurations for testing.
    /// </summary>
    private static void ConfigureTestServices(IServiceCollection services)
    {
        RemoveService<IEmailService>(services);

        RemoveService<ISmsService>(services);

        RemoveService<IChannelFactory>(services);

        var mockEmailService = new Mock<IEmailService>();
        services.AddSingleton(mockEmailService.Object);
        
        var mockSmsService = new Mock<ISmsService>();
        services.AddSingleton(mockSmsService.Object);
        
        var mockChannelFactoryService = new Mock<IChannelFactory>();
        services.AddSingleton(mockChannelFactoryService.Object);
    }

    /// <summary>
    /// Configures services related to the database.
    /// </summary>
    private void ConfigureDatabaseServices(IServiceCollection services)
    {
        RemoveService<DbContextOptions<PortalDbContext>>(services);

        services.AddDbContext<PortalDbContext>(options =>
        {
            options.UseNpgsql(_databaseContainer.ConnectionString!)
                   .EnableSensitiveDataLogging()
                   .EnableDetailedErrors();
        });
    }

    /// <summary>
    /// Registers the services required for testing.
    /// </summary>
    private static void RegisterTestServices(IServiceCollection services)
    {
        services.AddScoped<ITestDataSeeder, TestDataSeeder>();
    }

    #endregion

    #region Database Initialization

    /// <summary>
    /// Prepares the test database and seeds sample data.
    /// </summary>
    private async Task InitializeDatabaseAsync(IServiceProvider services)
    {
        _logger.LogInformation("Starting test database initialization...");

        try
        {
            var testDbContext = services.GetRequiredService<PortalDbContext>();
            var testDataSeeder = services.GetRequiredService<ITestDataSeeder>();

            _logger.LogInformation("Resetting test database...");
            await testDbContext.Database.EnsureDeletedAsync();
            await testDbContext.Database.EnsureCreatedAsync();

            _logger.LogInformation("Seeding test data...");
            await testDataSeeder.SeedAsync();

            _logger.LogInformation("Test database initialized successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize test database");
            throw;
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Removes a specific service from the service collection.
    /// </summary>
    private static void RemoveService<T>(IServiceCollection services)
    {
        var descriptor = services.FirstOrDefault(s => s.ServiceType == typeof(T));
        if (descriptor != null)
        {
            services.Remove(descriptor);
        }
    }

    #endregion
}
