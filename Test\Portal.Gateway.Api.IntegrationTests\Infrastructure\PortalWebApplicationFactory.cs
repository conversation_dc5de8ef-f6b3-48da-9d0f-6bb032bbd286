using Gateway.EventBus.Connections;
using Moq;
using Portal.Gateway.ExternalServices.Contracts;

namespace Portal.Gateway.Api.IntegrationTests.Infrastructure;

/// <summary>
/// Test factory for integration tests that manages test database lifecycle
/// and provides proper service configuration for testing.
/// </summary>
public sealed class PortalWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
{
    private readonly DatabaseContainer _databaseContainer = new();
    private readonly ILogger<PortalWebApplicationFactory> _logger;
    private readonly ILoggerFactory _loggerFactory;

    public PortalWebApplicationFactory()
    {
        _loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = _loggerFactory.CreateLogger<PortalWebApplicationFactory>();
    }

    protected override IHost CreateHost(IHostBuilder builder)
    {
        builder.ConfigureServices(ConfigureTestServices);

        var host = builder.Build();
        host.Start();

        // Initialize database synchronously to avoid deadlock issues
        using var scope = host.Services.CreateScope();
        Task.Run(async () => await InitializeDatabaseAsync(scope.ServiceProvider)).GetAwaiter().GetResult();

        return host;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureTestServices(services =>
        {
            ConfigureDatabaseServices(services);
            RegisterTestServices(services);
        });
    }

    public async Task InitializeAsync() => await _databaseContainer.InitializeAsync();

    public new async Task DisposeAsync()
    {
        await _databaseContainer.DisposeAsync();
        _loggerFactory?.Dispose();
        await base.DisposeAsync();
    }

    #region Service Configuration

    /// <summary>
    /// Configures the necessary service configurations for testing.
    /// </summary>
    private static void ConfigureTestServices(IServiceCollection services)
    {
        // Replace services with mocks
        ReplaceWithMock<IEmailService>(services);
        ReplaceWithMock<ISmsService>(services);
        ReplaceWithMock<IChannelFactory>(services);
    }

    /// <summary>
    /// Configures services related to the database.
    /// </summary>
    private void ConfigureDatabaseServices(IServiceCollection services)
    {
        RemoveService<DbContextOptions<PortalDbContext>>(services);

        services.AddDbContext<PortalDbContext>(options =>
        {
            options.UseNpgsql(_databaseContainer.ConnectionString!)
                   .EnableSensitiveDataLogging()
                   .EnableDetailedErrors();
        });
    }

    /// <summary>
    /// Registers the services required for testing.
    /// </summary>
    private static void RegisterTestServices(IServiceCollection services)
    {
        services.AddScoped<ITestDataSeeder, TestDataSeeder>();
    }

    #endregion

    #region Database Initialization

    /// <summary>
    /// Prepares the test database and seeds sample data.
    /// </summary>
    private async Task InitializeDatabaseAsync(IServiceProvider services)
    {
        _logger.LogInformation("Starting test database initialization...");

        try
        {
            var testDbContext = services.GetRequiredService<PortalDbContext>();
            var testDataSeeder = services.GetRequiredService<ITestDataSeeder>();

            _logger.LogInformation("Resetting test database...");
            await testDbContext.Database.EnsureDeletedAsync();
            await testDbContext.Database.EnsureCreatedAsync();

            _logger.LogInformation("Seeding test data...");
            await testDataSeeder.SeedAsync();

            _logger.LogInformation("Test database initialized successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize test database");
            throw;
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Removes a specific service from the service collection.
    /// </summary>
    private static void RemoveService<T>(IServiceCollection services)
    {
        // More efficient: iterate backwards to avoid index shifting issues
        for (int i = services.Count - 1; i >= 0; i--)
        {
            if (services[i].ServiceType == typeof(T))
            {
                services.RemoveAt(i);
            }
        }
    }

    /// <summary>
    /// Removes a service and replaces it with a mock implementation.
    /// </summary>
    private static void ReplaceWithMock<T>(IServiceCollection services) where T : class
    {
        RemoveService<T>(services);
        var mock = new Mock<T>();
        services.AddSingleton(mock.Object);
    }

    #endregion
}
