namespace Portal.Gateway.Api.IntegrationTests.Infrastructure;

/// <summary>  
/// Base class for all integration tests  
/// </summary>
[Collection(nameof(IntegrationTestCollection))]
public abstract class BaseIntegrationTest : IDisposable
{
    protected readonly PortalDbContext DbContext;
    protected readonly HttpClient HttpClient;
    protected readonly ITestDataSeeder TestDataSeeder;
    private readonly IServiceScope _serviceScope;
    protected static readonly Faker Faker = new();

    protected BaseIntegrationTest(SharedTestFixture fixture)
    {
        _serviceScope = fixture.Factory.Services.CreateScope();
        DbContext = _serviceScope.ServiceProvider.GetRequiredService<PortalDbContext>();
        TestDataSeeder = _serviceScope.ServiceProvider.GetRequiredService<ITestDataSeeder>();
        HttpClient = fixture.Factory.ConfigureHttpClient();
    }

    /// <summary>  
    /// Cleans up resources used for the test  
    /// </summary>
    public virtual void Dispose()
    {
        if (HttpClient is IDisposable client)
            client.Dispose();

        _serviceScope?.Dispose();
        GC.SuppressFinalize(this);
    }
}