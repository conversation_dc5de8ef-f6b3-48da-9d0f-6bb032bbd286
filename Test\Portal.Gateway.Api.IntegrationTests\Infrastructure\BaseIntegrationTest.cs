namespace Portal.Gateway.Api.IntegrationTests.Infrastructure;

/// <summary>
/// Base class for all integration tests
/// </summary>
[Collection(nameof(IntegrationTestCollection))]
public abstract class BaseIntegrationTest : IAsyncDisposable, IDisposable
{
    protected readonly PortalDbContext DbContext;
    protected readonly HttpClient HttpClient;
    protected readonly ITestDataSeeder TestDataSeeder;
    private readonly IServiceScope _serviceScope;

    // Thread-safe Faker instance per test class
    protected readonly Faker Faker = new();

    protected BaseIntegrationTest(SharedTestFixture fixture)
    {
        _serviceScope = fixture.Factory.Services.CreateScope();
        DbContext = _serviceScope.ServiceProvider.GetRequiredService<PortalDbContext>();
        TestDataSeeder = _serviceScope.ServiceProvider.GetRequiredService<ITestDataSeeder>();
        HttpClient = fixture.Factory.ConfigureHttpClient();
    }

    /// <summary>
    /// Cleans up resources used for the test asynchronously
    /// </summary>
    public virtual async ValueTask DisposeAsync()
    {
        // Dispose DbContext if it supports async disposal
        if (DbContext is IAsyncDisposable asyncDbContext)
            await asyncDbContext.DisposeAsync();
        else
            DbContext?.Dispose();

        // HttpClient is managed by the factory, don't dispose it manually
        // _serviceScope handles its own disposal
        _serviceScope?.Dispose();

        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Synchronous dispose implementation for IDisposable compatibility
    /// </summary>
    public virtual void Dispose()
    {
        // Use async disposal when possible
        DisposeAsync().AsTask().GetAwaiter().GetResult();
        GC.SuppressFinalize(this);
    }
}