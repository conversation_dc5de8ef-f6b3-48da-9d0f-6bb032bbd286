using Portal.Gateway.ApiModel.Requests.Management.Branch;

namespace Portal.Gateway.Api.IntegrationTests.Modules.Management;

public sealed class BranchTest : BaseIntegrationTest
{
    public BranchTest(SharedTestFixture fixture) : base(fixture) { }

    #region Add Branch Test

    [Fact]
    internal async Task AddBranch_WhenBranchIsValid_ShouldAddBranch()
    {
        // Arrange
        var country = TestDataSeeder.GetRandomCountry();
        var provider = TestDataSeeder.GetRandomProvider();

        var request = GenerateAddBranchApiRequest();
        request.CountryId = country.Id;
        request.InsuranceProviderId = provider.Id;

        // Act
        var result = await HttpClient.PostAndGetResultAsync<AddResponseDto>(ApiMethodName.Management.AddBranch, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Id.Should().BePositive();

        var addedBranch = DbContext.Branch
            .AsNoTracking()
            .Include(i => i.BranchTranslations)
            .Include(i => i.BranchDataTranslation)
            .OrderByDescending(o => o.Id)
            .First();

        request.Should().BeEquivalentTo(addedBranch,
            options => options.ExcludingMissingMembers()
            .Excluding(e => e.DisableContactInformationVerification));

        addedBranch.CreatedAt.Should().NotBeNull();
        addedBranch.CreatedBy.Should().NotBeNull();
        addedBranch.IsActive.Should().BeTrue();
    }

    [Fact]
    internal async Task AddBranch_WhenBranchAlreadyExists_ShouldReturnAlreadyExistsDataError()
    {
        // Arrange
        var provider = TestDataSeeder.GetRandomProvider();

        var existingBranch = DbContext.Branch.Include(b => b.BranchTranslations).First();

        var request = GenerateAddBranchApiRequest();
        request.CountryId = existingBranch.CountryId;
        request.InsuranceProviderId = provider.Id;
        request.BranchTranslations[0].Name = existingBranch.BranchTranslations.First().Name;
        request.BranchTranslations[0].LanguageId = existingBranch.BranchTranslations.First().LanguageId;

        // Act
        var result = await HttpClient.PostAndGetResultAsync<AddResponseDto>(ApiMethodName.Management.AddBranch, request);

        // Assert

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.AlreadyExistsData);
        result.Data.Should().BeNull();
    }

    [Fact]
    internal async Task AddBranch_WhenRequiredFieldsAreMissing_ShouldReturnValidationError()
    {
        // Arrange
        var country = TestDataSeeder.GetRandomCountry();
        var provider = TestDataSeeder.GetRandomProvider();

        var request = GenerateAddBranchApiRequest();
        request.CountryId = country.Id;
        request.InsuranceProviderId = provider.Id;
        request.Email = null;
        request.Telephone = null;

        // Act
        var result = await HttpClient.PostAndGetResultAsync<AddResponseDto>(ApiMethodName.Management.AddBranch, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.ValidationFailed);
        result.Data.Should().BeNull();
    }

    #endregion

    #region Update Branch Test
    [Fact]
    internal async Task UpdateBranch_WhenBranchIsValid_ShouldUpdateBranch()
    {
        // Arrange
        var existingBranch = CreateTestBranch();
        var request = GenerateUpdateBranchApiRequest();
        request.Id = existingBranch.Id;
        request.CountryId = existingBranch.CountryId;
        request.BranchTranslations[0].Id = existingBranch.BranchTranslations.First().Id;
        request.BranchTranslations[0].LanguageId = existingBranch.BranchTranslations.First().LanguageId;

        // Use different values for update
        request.Email = Faker.Internet.Email();
        request.Address = Faker.Address.StreetAddress();

        // Act
        var result = await HttpClient.PutAndGetResultAsync<UpdateResponseDto>(ApiMethodName.Management.UpdateBranch, request);

        // Assert

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Result.Should().BeTrue();

        var updatedBranch = DbContext.Branch
            .AsNoTracking()
            .Include(b => b.BranchTranslations)
            .Include(b => b.BranchDataTranslation)
            .First(b => b.Id == existingBranch.Id);

        request.Should().BeEquivalentTo(updatedBranch,
            options => options.ExcludingMissingMembers());

        // Verify updated fields
        updatedBranch.UpdatedAt.Should().NotBeNull();
        updatedBranch.UpdatedBy.Should().NotBeNull();
    }

    [Fact]
    internal async Task UpdateBranch_WhenBranchAlreadyExists_ShouldReturnAlreadyExistsDataError()
    {
        // Arrange
        var branchToUpdate = CreateTestBranch();

        var existingBranch = CreateTestBranch();

        var request = GenerateUpdateBranchApiRequest();
        request.Id = branchToUpdate.Id;
        request.CountryId = existingBranch.CountryId;
        request.BranchTranslations[0].Name = existingBranch.BranchTranslations.First().Name;
        request.BranchTranslations[0].LanguageId = existingBranch.BranchTranslations.First().LanguageId;

        // Act
        var result = await HttpClient.PutAndGetResultAsync<UpdateResponseDto>(ApiMethodName.Management.UpdateBranch, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.AlreadyExistsData);
    }

    [Fact]
    internal async Task UpdateBranch_WhenBranchNotFound_ShouldReturnNoFoundDataError()
    {
        // Arrange
        var request = GenerateUpdateBranchApiRequest();
        request.Id = int.MaxValue;

        // Act
        var result = await HttpClient.PutAndGetResultAsync<UpdateResponseDto>(ApiMethodName.Management.UpdateBranch, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.NoFoundData);
    }

    [Fact]
    internal async Task UpdateBranch_WhenRequiredFieldsAreMissing_ShouldReturnValidationError()
    {
        // Arrange
        var existingBranch = CreateTestBranch();
        var request = GenerateUpdateBranchApiRequest();
        request.Id = existingBranch.Id;
        request.CountryId = existingBranch.CountryId;
        request.Email = null;
        request.Telephone = null;

        // Act
        var result = await HttpClient.PutAndGetResultAsync<UpdateResponseDto>(ApiMethodName.Management.UpdateBranch, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.ValidationFailed);
        result.Data.Should().BeNull();
    }

    #endregion

    #region Delete Branch Test

    [Fact]
    internal async Task DeleteBranch_WhenBranchExists_ShouldDeleteBranch()
    {
        // Arrange
        var existingBranch = CreateTestBranch();

        // Act
        var result = await HttpClient.DeleteAndGetResultAsync<DeleteResponseDto>(ApiMethodName.Management.DeleteBranch + existingBranch.Id);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Result.Should().BeTrue();

        var deletedBranch = DbContext.Branch
            .AsNoTracking()
            .First(b => b.Id == existingBranch.Id);

        deletedBranch.IsDeleted.Should().BeTrue();
        deletedBranch.DeletedAt.Should().NotBeNull();
        deletedBranch.DeletedBy.Should().NotBeNull();
    }

    [Fact]
    internal async Task DeleteBranch_WhenBranchNotExists_ShouldReturnNoFoundDataError()
    {
        // Arrange
        var nonExistingBranchId = int.MaxValue;

        // Act
        var result = await HttpClient.DeleteAndGetResultAsync<DeleteResponseDto>(ApiMethodName.Management.DeleteBranch + nonExistingBranchId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.NoFoundData);
    }

    #endregion

    #region Get Branch Tests

    [Fact]
    internal async Task GetBranch_WhenBranchExists_ShouldReturnBranch()
    {
        // Arrange
        var branch = CreateTestBranch();

        // Act
        var result = await HttpClient.GetAndDeserializeAsync<BranchApiResponse>(ApiMethodName.Management.GetBranch + branch.Id);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();

        result.Data.Should().BeEquivalentTo(branch,
            options => options.ExcludingMissingMembers()
                .Excluding(e => e.Country)
                .Excluding(e => e.CountryId));

        result.Data.Country.Id.Should().Be(branch.CountryId);
    }

    [Fact]
    internal async Task GetBranch_WhenBranchNotExists_ShouldReturnNotFoundError()
    {
        // Arrange
        var nonExistingBranchId = int.MaxValue;

        // Act
        var result =
            await HttpClient.GetAndDeserializeAsync<BranchApiResponse>(ApiMethodName.Management.GetBranch +
                                                                       nonExistingBranchId);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Code.Should().Be(ApiErrorCodes.NoFoundData);
        result.Data.Should().BeNull();
    }

    #endregion

    #region Get Paginated Branches Tests

    [Fact]
    internal async Task GetPaginatedBranches_WhenNoBranchesExist_ShouldReturnEmptyPagination()
    {
        // Arrange
        var request = new PaginatedBranchesApiRequest
        {
            CountryId = int.MaxValue,
            Pagination = new PaginationApiRequest
            {
                Page = 1,
                PageSize = 5
            }
        };

        // Act
        DbContext.BranchDataTranslation.RemoveRange(DbContext.BranchDataTranslation);
        DbContext.BranchTranslation.RemoveRange(DbContext.BranchTranslation);
        DbContext.Branch.RemoveRange(DbContext.Branch);

        var result = await HttpClient.PostAndGetResultAsync<PaginationApiResponse<BranchesApiResponse>>(ApiMethodName.Management.GetPaginatedBranches, request);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Items.Should().NotBeNull();
        result.Data.Items.First().Branches.Should().BeEmpty();
        result.Data.TotalItemCount.Should().Be(0);
    }

    [Fact]
    internal async Task GetPaginatedBranches_WhenBranchesExist_ShouldReturnPaginatedBranches()
    {
        // Arrange
        int pageSize = 2;
        var request = new PaginatedBranchesApiRequest
        {
            Pagination = new PaginationApiRequest
            {
                Page = 1,
                PageSize = pageSize
            }
        };

        // Act
        _ = Enumerable.Range(0, pageSize).Select(_ => CreateTestBranch());

        var result = await HttpClient.PostAndGetResultAsync<PaginationApiResponse<BranchesApiResponse>>(ApiMethodName.Management.GetPaginatedBranches, request);

        // Assert
       
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Items.Should().NotBeEmpty();
        result.Data.Items.First().Branches.Should().HaveCount(pageSize);
        result.Data.Page.Should().Be(request.Pagination.Page);
    }

    #endregion


    #region Private Methods
    private AddBranchApiRequest GenerateAddBranchApiRequest()
    {
        return new AddBranchApiRequest
        {
            CountryId = Faker.Random.Int(1, 150),
            Address = Faker.Address.StreetAddress(),
            Email = Faker.Internet.Email(),
            Telephone = Faker.Phone.PhoneNumber(),
            CityName = Faker.Address.City(),
            Mission = Faker.Company.CatchPhrase(),
            CorporateName = Faker.Company.CompanyName(),
            InvoiceNumber = Faker.Random.AlphaNumeric(10),
            SapBranchId = Faker.Random.AlphaNumeric(10),
            TimeZoneOffset = Faker.Random.Decimal(),
            CheckRejectedStatus = true,
            CheckRejectedStatusPeriod = Faker.Random.Int(15, 60),
            CheckUnrealDocumentStatus = true,
            CheckUnrealDocumentStatusPeriod = Faker.Random.Int(15, 60),
            CheckRejectionWithCountryEntryBannedStatus = true,
            CheckRejectionWithCountryEntryBannedStatusPeriod = Faker.Random.Int(15, 60),
            RejectionRefundDonePermissionNumber = Faker.Random.Int(1, 100),
            IsValidAMS = true,
            IsSlotTypesConnected = true,
            ConnectedSlotTypeId = 1,
            IsPassportScanRequired = true,
            QmsCompanyId = (byte)Faker.Random.Int(1, 2),
            EmailProviderId = Faker.Random.Int(1, 2),
            SmsProviderId = Faker.Random.Int(1, 2),
            QmsWalkinVipControl = true,
            SmsSender = Faker.Random.AlphaNumeric(10),
            InsuranceProviderId = Faker.Random.Int(1, 2),
            DisableContactInformationVerification = true,
            BranchTranslations = new List<AddBranchTranslationApiRequest>
            {
                new() { LanguageId = 1, Name = Faker.Company.CompanyName() }
            },
            BranchDataTranslation = new List<AddBranchDataTranslationApiRequest>
            {
                new()
                {
                    LanguageId = 1,
                    Address = Faker.Address.StreetAddress(),
                    CorporateName = Faker.Company.CompanyName(),
                    InvoiceNumber = Faker.Random.AlphaNumeric(10),
                    Mission = Faker.Company.CatchPhrase(),
                    CityName = Faker.Address.City()
                }
            }
        };
    }
    private UpdateBranchApiRequest GenerateUpdateBranchApiRequest()
    {
        return new UpdateBranchApiRequest
        {
            Id = Faker.Random.Int(1, 100),
            CountryId = Faker.Random.Int(1, 150),
            Address = Faker.Address.StreetAddress(),
            Email = Faker.Internet.Email(),
            Telephone = Faker.Phone.PhoneNumber(),
            CityName = Faker.Address.City(),
            Mission = Faker.Company.CatchPhrase(),
            CorporateName = Faker.Company.CompanyName(),
            InvoiceNumber = Faker.Random.AlphaNumeric(10),
            SapBranchId = Faker.Random.AlphaNumeric(10),
            TimeZoneOffset = Faker.Random.Decimal(),
            CheckRejectedStatus = true,
            CheckRejectedStatusPeriod = Faker.Random.Int(15, 60),
            CheckUnrealDocumentStatus = true,
            CheckUnrealDocumentStatusPeriod = Faker.Random.Int(15, 60),
            CheckRejectionWithCountryEntryBannedStatus = true,
            CheckRejectionWithCountryEntryBannedStatusPeriod = Faker.Random.Int(15, 60),
            RejectionRefundDonePermissionNumber = Faker.Random.Int(1, 100),
            IsValidAMS = true,
            IsSlotTypesConnected = true,
            ConnectedSlotTypeId = 1,
            IsPassportScanRequired = true,
            QmsCompanyId = (byte)Faker.Random.Int(1, 2),
            EmailProviderId = Faker.Random.Int(1, 2),
            SmsProviderId = Faker.Random.Int(1, 2),
            QmsWalkinVipControl = true,
            SmsSender = Faker.Random.AlphaNumeric(10),
            InsuranceProviderId = Faker.Random.Int(1, 2),
            IsCargoIntegrationActive = true,
            ShowCityDropdown = true,
            IsPrintAllIntegrationActive = true,
            IsPhotoBoothIntegrationActive = true,
            IsApplicationUpdateStatusCheckActive = true,
            IsPrinterIntegrationActive = true,
            IsPreApplicationConnectionActive = true,
            IsVisaRejectionDocumentsControl = true,
            IsRejectionApprovalControl = true,
            IsActive = true,
            DisableContactInformationVerification = true,
            ShowInB2c = true,
            SendBasicGuidelineInAms = true,
            IsDigitalSignatureIntegrationActive = true,
            IsRelatedInsuranceForExempt = true,
            ShowPaymentMethods = true,
            QmsScreenTitle = Faker.Company.CompanyName(),
            ShowBranchInMobile = true,
            BranchTranslations = new List<UpdateBranchTranslationApiRequest>
            {
                new() { Id = 1, LanguageId = 1, Name = Faker.Company.CompanyName() }
            },
            BranchDataTranslations = new List<UpdateBranchDataTranslationApiRequest>
            {
                new()
                {
                    Id = 1,
                    Address = Faker.Address.StreetAddress(),
                    CorporateName = Faker.Company.CompanyName(),
                    InvoiceNumber = Faker.Random.AlphaNumeric(10),
                    Mission = Faker.Company.CatchPhrase(),
                    CityName = Faker.Address.City(),
                    LanguageId = 1
                }
            }
        };
    }
    private Branch CreateTestBranch()
    {
        var country = TestDataSeeder.GetRandomCountry();
        var provider = TestDataSeeder.GetRandomProvider();

        var branch = new Entity.Entities.Portal.Branch
        {
            CountryId = country.Id,
            InsuranceProviderId = provider.Id,
            Address = Faker.Address.StreetAddress(),
            Email = Faker.Internet.Email(),
            Telephone = Faker.Phone.PhoneNumber(),
            CityName = Faker.Address.City(),
            Mission = Faker.Company.CatchPhrase(),
            InvoiceNumber = Faker.Random.AlphaNumeric(4),
            CorporateName = Faker.Company.CompanyName(),
            BranchTranslations = new List<BranchTranslation>
            {
                new()
                {
                    LanguageId = Faker.Random.Int(1, 2),
                    Name = Faker.Company.CompanyName()
                }
            },
            BranchDataTranslation = new List<BranchDataTranslation>
            {
                new()
                {
                    LanguageId = Faker.Random.Int(1, 2),
                    Address = Faker.Address.StreetAddress(),
                    CorporateName = Faker.Company.CompanyName(),
                    InvoiceNumber = Faker.Random.AlphaNumeric(4),
                    Mission = Faker.Company.CatchPhrase(),
                    CityName = Faker.Address.City()
                }
            },
        };

        DbContext.Branch.Add(branch);
        DbContext.SaveChanges();
        return branch;
    }

    #endregion
}


